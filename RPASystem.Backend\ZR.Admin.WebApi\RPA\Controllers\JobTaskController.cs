using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using RPASystem.Model;
using RPASystem.Service;
using RPASystem.Model;
using RPASystem.WebApi.Hubs;
using System.Collections.Generic;

namespace Controllers
{
    /// <summary>
    /// 任务控制器，处理任务相关的API请求
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class JobTaskController : ControllerBase
    {
        private readonly IJobTaskService jobTaskService;
        private readonly IHubContext<ResourceMachineHub> hubContext;

        public JobTaskController(IJobTaskService jobTaskService, IHubContext<ResourceMachineHub> hubContext)
        {
            this.jobTaskService = jobTaskService;
            this.hubContext = hubContext;
        }

        [HttpGet("all")]
        public IActionResult GetAllJobTasks()
        {
            var jobTasks = jobTaskService.GetAllJobTasksWithExePrograms();
            return Ok(jobTasks);
        }

        [HttpPost("create")]
        public async Task<IActionResult> CreateJobTask([FromBody] JobTaskCreateDto dto)
        {
            try 
            {
                // 解析输入参数以检查任务类型
                var inputParams = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(dto.InputParameters);
                
                var jobTask = new JobTask
                {
                    ExeProgramID = dto.ExeProgramId,
                    TaskName = DateTime.Now.ToString("yyyyMMddhhmmss"),
                    Priority = dto.Priority,
                    InputParameters = dto.InputParameters,
                    Status = JobTaskStatusEnum.Pending,
                    CreatedAt = DateTime.Now,
                    TaskType = dto.TaskType,
                    ResourceSelection = dto.ResourceSelection
                };

                // 如果是系统编排拆分任务，需要验证必要参数
                if (dto.TaskType == TaskType.SystemOrchestrationSplit)
                {
                    if (!inputParams.ContainsKey("InputFile") || 
                        !inputParams.ContainsKey("ExcelPerSplitNum") || 
                        !inputParams.ContainsKey("MergeType"))
                    {
                        return BadRequest("系统编排拆分任务缺少必要参数");
                    }
                }

                var id = await jobTaskService.CreateJobTaskAsync(jobTask);
                return Ok(new { Id = id });
            }
            catch (Exception ex)
            {
                return BadRequest($"创建任务失败: {ex.Message}");
            }
        }

        [HttpDelete("{id}")]
        public IActionResult DeleteJobTask(long id)
        {
            var result = jobTaskService.DeleteJobTask(id);
            if (result)
            {
                return Ok();
            }
            return NotFound();
        }

        [HttpPost("retry/{id}")]
        public async Task<IActionResult> RetryJobTask(long id)
        {
            try
            {
                var result = await jobTaskService.RetryJobTaskAsync(id);
                if (!result)
                {
                    return NotFound("任务不存在或重试失败");
                }
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest($"重试任务失败: {ex.Message}");
            }
        }

        [HttpPost("stop/{id}")]
        public async Task<IActionResult> StopJobTask(long id)
        {
            try 
            {
                // 获取所有需要停止的任务（主任务和子任务）
                var tasksToStop = await jobTaskService.GetTasksToStopAsync(id);
                if (!tasksToStop.Any())
                {
                    return BadRequest("没有需要停止的任务");
                }

                // 遍历所有需要停止的任务
                foreach (var task in tasksToStop)
                {
                    // 如果任务正在运行且已分配资源机器，需要通知资源机器
                    if (task.Status == JobTaskStatusEnum.Running && 
                        !string.IsNullOrEmpty(task.AssignedResourceMachine))
                    {
                        if (ResourceMachineHub.machineConnections.TryGetValue(
                            task.AssignedResourceMachine, out string connectionId))
                        {
                            await hubContext.Clients.Client(connectionId).SendAsync("StopTask", task.ID);
                            await jobTaskService.UpdateJobTaskStatusAsync(task.ID, JobTaskStatusEnum.Cancelling);
                            continue;
                        }
                    }

                    // 如果任务未运行或未分配资源机器，直接设置为已取消
                    await jobTaskService.UpdateJobTaskStatusAsync(task.ID, JobTaskStatusEnum.Cancelled);
                }

                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest($"停止任务失败: {ex.Message}");
            }
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchJobTasks([FromQuery] JobTaskQueryParams queryParams)
        {
            var (items, totalCount) = await jobTaskService.GetJobTasksPagedAsync(queryParams);
            return Ok(new { 
                Items = items, 
                TotalCount = totalCount,
                PageSize = queryParams.PageSize,
                PageNumber = queryParams.PageNumber
            });
        }

        [HttpPut("update/{id}")]
        public async Task<IActionResult> UpdateJobTask(long id, [FromBody] JobTaskUpdateDto dto)
        {
            try
            {
                var result = await jobTaskService.UpdateJobTaskWithChildrenAsync(id, dto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest($"更新任务失败: {ex.Message}");
            }
        }

        [HttpGet("subtasks")]
        public async Task<IActionResult> GetSubTasks([FromQuery] SubTaskQueryParams queryParams)
        {
            try
            {
                var (items, totalCount) = await jobTaskService.GetSubTasksPagedAsync(queryParams);
                return Ok(new
                {
                    Items = items,
                    TotalCount = totalCount,
                    PageSize = queryParams.PageSize,
                    PageNumber = queryParams.PageNumber
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}
